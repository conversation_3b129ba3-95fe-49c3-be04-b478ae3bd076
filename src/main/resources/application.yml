spring:
  application:
    name: movie-ticket-booking-system-api

  datasource:
    url: *************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    open-in-view: false
    show-sql: true
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        format_sql: true

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:pguQuMEr0QorbK6ZrCPITXw5/NRf7zRW2yjh4/WpN4c=}
  expiration: ${JWT_EXPIRATION:86400000} # 24 hours in milliseconds

# Logging Configuration
logging:
  level:
    com.example.movieticketbookingsystem: DEBUG
    org.springframework.security: DEBUG

