package com.example.movieticketbookingsystem.exception.handler;

import com.example.movieticketbookingsystem.utility.ErrorStructure;
import com.example.movieticketbookingsystem.utility.RestResponseBuilder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Exception handler specifically for security-related exceptions.
 * This handler provides detailed error responses for authentication and authorization failures.
 */
@RestControllerAdvice
@AllArgsConstructor
@Slf4j
@Order(4)
public class SecurityExceptionHandler {

    private final RestResponseBuilder restResponseBuilder;

    /**
     * Handles BadCredentialsException when login credentials are invalid.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ErrorStructure> handleBadCredentials(
            BadCredentialsException ex, HttpServletRequest request) {
        
        log.warn("Bad credentials attempt from IP: {}", request.getRemoteAddr());
        
        return restResponseBuilder.error(
                HttpStatus.UNAUTHORIZED,
                "Invalid username or password. Please check your credentials and try again."
        );
    }

    /**
     * Handles general AuthenticationException.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ErrorStructure> handleAuthenticationException(
            AuthenticationException ex, HttpServletRequest request) {
        
        log.error("Authentication failed: {}", ex.getMessage());
        
        String errorMessage = "Authentication failed";
        
        // Provide specific messages for different authentication failures
        if (ex instanceof DisabledException) {
            errorMessage = "Your account has been disabled. Please contact support.";
        } else if (ex instanceof LockedException) {
            errorMessage = "Your account has been locked. Please contact support.";
        } else if (ex.getMessage() != null) {
            errorMessage = "Authentication failed: " + ex.getMessage();
        }
        
        return restResponseBuilder.error(HttpStatus.UNAUTHORIZED, errorMessage);
    }

    /**
     * Handles AccessDeniedException when user doesn't have required permissions.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ErrorStructure> handleAccessDenied(
            AccessDeniedException ex, HttpServletRequest request) {
        
        log.warn("Access denied for user attempting to access: {}", request.getRequestURI());
        
        return restResponseBuilder.error(
                HttpStatus.FORBIDDEN,
                "Access denied. You don't have permission to access this resource."
        );
    }
}
