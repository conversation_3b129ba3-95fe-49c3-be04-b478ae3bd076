package com.example.movieticketbookingsystem.exception.handler;

import com.example.movieticketbookingsystem.exception.MovieNotFoundByIdException;
import com.example.movieticketbookingsystem.utility.ErrorStructure;
import com.example.movieticketbookingsystem.utility.RestResponseBuilder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Exception handler specifically for movie-related exceptions.
 * This handler provides detailed error responses for movie management operations.
 */
@RestControllerAdvice
@AllArgsConstructor
@Slf4j
@Order(2)
public class MovieExceptionHandler {

    private final RestResponseBuilder restResponseBuilder;

    /**
     * Handles MovieNotFoundByIdException when a movie is not found by ID.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(MovieNotFoundByIdException.class)
    public ResponseEntity<ErrorStructure> handleMovieNotFoundById(
            MovieNotFoundByIdException ex, HttpServletRequest request) {
        
        log.error("Movie not found by ID: {}", ex.getMessage());
        
        String errorMessage = ex.getMessage() != null && !ex.getMessage().isEmpty() 
            ? ex.getMessage() 
            : "Movie not found with the provided ID";
            
        return restResponseBuilder.error(HttpStatus.NOT_FOUND, errorMessage);
    }
}
