package com.example.movieticketbookingsystem.exception.handler;

import com.example.movieticketbookingsystem.exception.ConflictException;
import com.example.movieticketbookingsystem.exception.ResourceNotFoundException;
import com.example.movieticketbookingsystem.utility.ErrorStructure;
import com.example.movieticketbookingsystem.utility.RestResponseBuilder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * Exception handler for general application exceptions and HTTP-related errors.
 * This handler provides responses for common web application exceptions.
 */
@RestControllerAdvice
@AllArgsConstructor
@Slf4j
@Order(10) // Lower priority to allow specific handlers to handle first
public class GeneralExceptionHandler {

    private final RestResponseBuilder restResponseBuilder;

    /**
     * Handles ResourceNotFoundException for general resource not found scenarios.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ErrorStructure> handleResourceNotFound(
            ResourceNotFoundException ex, HttpServletRequest request) {
        
        log.error("Resource not found: {}", ex.getMessage());
        
        String errorMessage = ex.getMessage() != null && !ex.getMessage().isEmpty() 
            ? ex.getMessage() 
            : "The requested resource was not found";
            
        return restResponseBuilder.error(HttpStatus.NOT_FOUND, errorMessage);
    }

    /**
     * Handles ConflictException for resource conflict scenarios.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(ConflictException.class)
    public ResponseEntity<ErrorStructure> handleConflictException(
            ConflictException ex, HttpServletRequest request) {
        
        log.warn("Resource conflict: {}", ex.getMessage());
        
        String errorMessage = ex.getMessage() != null && !ex.getMessage().isEmpty() 
            ? ex.getMessage() 
            : "A conflict occurred with the current state of the resource";
            
        return restResponseBuilder.error(
                HttpStatus.CONFLICT,
                errorMessage,
                request.getRequestURI()
        );
    }

    /**
     * Handles DataIntegrityViolationException for database constraint violations.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ErrorStructure> handleDataIntegrityViolation(
            DataIntegrityViolationException ex, HttpServletRequest request) {
        
        log.error("Data integrity violation: {}", ex.getMessage());
        
        String errorMessage = "Data integrity constraint violation. Please check your input data.";
        
        // Check for common constraint violations
        if (ex.getMessage() != null) {
            if (ex.getMessage().contains("Duplicate entry")) {
                errorMessage = "Duplicate entry detected. The resource already exists.";
            } else if (ex.getMessage().contains("foreign key constraint")) {
                errorMessage = "Referenced resource does not exist.";
            } else if (ex.getMessage().contains("not null")) {
                errorMessage = "Required field cannot be null.";
            }
        }
        
        return restResponseBuilder.error(
                HttpStatus.BAD_REQUEST,
                errorMessage,
                request.getRequestURI()
        );
    }

    /**
     * Handles NoHandlerFoundException when no handler is found for the request.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ErrorStructure> handleNoHandlerFound(
            NoHandlerFoundException ex, HttpServletRequest request) {
        
        log.warn("No handler found for: {} {}", ex.getHttpMethod(), ex.getRequestURL());
        
        return restResponseBuilder.error(
                HttpStatus.NOT_FOUND,
                String.format("No handler found for %s %s", ex.getHttpMethod(), ex.getRequestURL()),
                request.getRequestURI()
        );
    }

    /**
     * Handles HttpRequestMethodNotSupportedException for unsupported HTTP methods.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ErrorStructure> handleMethodNotSupported(
            HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {
        
        log.warn("Method not supported: {} for {}", ex.getMethod(), request.getRequestURI());
        
        return restResponseBuilder.error(
                HttpStatus.METHOD_NOT_ALLOWED,
                String.format("HTTP method '%s' is not supported for this endpoint. Supported methods: %s", 
                    ex.getMethod(), String.join(", ", ex.getSupportedMethods())),
                request.getRequestURI()
        );
    }

    /**
     * Handles MissingServletRequestParameterException for missing required parameters.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ErrorStructure> handleMissingParameter(
            MissingServletRequestParameterException ex, HttpServletRequest request) {
        
        log.warn("Missing required parameter: {}", ex.getParameterName());
        
        return restResponseBuilder.error(
                HttpStatus.BAD_REQUEST,
                String.format("Required parameter '%s' of type '%s' is missing", 
                    ex.getParameterName(), ex.getParameterType()),
                request.getRequestURI()
        );
    }

    /**
     * Handles MethodArgumentTypeMismatchException for type conversion errors.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ErrorStructure> handleTypeMismatch(
            MethodArgumentTypeMismatchException ex, HttpServletRequest request) {
        
        log.warn("Type mismatch for parameter: {}", ex.getName());
        
        return restResponseBuilder.error(
                HttpStatus.BAD_REQUEST,
                String.format("Invalid value '%s' for parameter '%s'. Expected type: %s", 
                    ex.getValue(), ex.getName(), ex.getRequiredType().getSimpleName()),
                request.getRequestURI()
        );
    }

    /**
     * Handles RuntimeException for general runtime errors.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ErrorStructure> handleRuntimeException(
            RuntimeException ex, HttpServletRequest request) {
        
        log.error("Runtime exception occurred: ", ex);
        
        return restResponseBuilder.error(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "An unexpected error occurred while processing your request",
                request.getRequestURI()
        );
    }

    /**
     * Handles all other exceptions not caught by specific handlers.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorStructure> handleGenericException(
            Exception ex, HttpServletRequest request) {
        
        log.error("Unexpected exception occurred: ", ex);
        
        return restResponseBuilder.error(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "An unexpected error occurred. Please try again later or contact support if the problem persists.",
                request.getRequestURI()
        );
    }
}
