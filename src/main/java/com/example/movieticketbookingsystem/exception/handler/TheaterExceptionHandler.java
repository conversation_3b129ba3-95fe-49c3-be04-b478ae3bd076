package com.example.movieticketbookingsystem.exception.handler;

import com.example.movieticketbookingsystem.exception.TheaterOwnerIdException;
import com.example.movieticketbookingsystem.exception.ScreenIdNotFoundException;
import com.example.movieticketbookingsystem.utility.ErrorStructure;
import com.example.movieticketbookingsystem.utility.RestResponseBuilder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Exception handler specifically for theater and screen-related exceptions.
 * This handler provides detailed error responses for theater management operations.
 */
@RestControllerAdvice
@AllArgsConstructor
@Slf4j
@Order(3)
public class TheaterExceptionHandler {

    private final RestResponseBuilder restResponseBuilder;

    /**
     * Handles TheaterOwnerIdException when a theater owner is not found by ID.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(TheaterOwnerIdException.class)
    public ResponseEntity<ErrorStructure> handleTheaterOwnerIdException(
            TheaterOwnerIdException ex, HttpServletRequest request) {
        
        log.error("Theater owner not found: {}", ex.getMessage());
        
        String errorMessage = ex.getMessage() != null && !ex.getMessage().isEmpty() 
            ? ex.getMessage() 
            : "Theater owner not found with the provided ID";
            
        return restResponseBuilder.error(HttpStatus.NOT_FOUND, errorMessage);
    }

    /**
     * Handles ScreenIdNotFoundException when a screen is not found by ID.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(ScreenIdNotFoundException.class)
    public ResponseEntity<ErrorStructure> handleScreenIdNotFound(
            ScreenIdNotFoundException ex, HttpServletRequest request) {
        
        log.error("Screen not found: {}", ex.getMessage());
        
        String errorMessage = ex.getMessage() != null && !ex.getMessage().isEmpty() 
            ? ex.getMessage() 
            : "Screen not found with the provided ID";
            
        return restResponseBuilder.error(HttpStatus.NOT_FOUND, errorMessage);
    }
}
