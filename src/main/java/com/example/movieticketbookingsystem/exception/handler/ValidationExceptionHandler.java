package com.example.movieticketbookingsystem.exception.handler;

import com.example.movieticketbookingsystem.utility.FieldErrorStructure;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Exception handler specifically for validation-related exceptions.
 * This handler provides detailed error responses for validation failures.
 */
@RestControllerAdvice
@Slf4j
@Order(5)
public class ValidationExceptionHandler {

    /**
     * Handles MethodArgumentNotValidException for @Valid annotation failures.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with detailed field error information
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<FieldErrorStructure<List<CustomFieldError>>> handleValidationErrors(
            MethodArgumentNotValidException ex, HttpServletRequest request) {
        
        log.warn("Validation failed for request to: {}", request.getRequestURI());
        
        List<CustomFieldError> fieldErrors = ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(fieldError -> CustomFieldError.builder()
                        .field(fieldError.getField())
                        .rejectedValue(fieldError.getRejectedValue())
                        .errorMessage(fieldError.getDefaultMessage())
                        .objectName(fieldError.getObjectName())
                        .build())
                .collect(Collectors.toList());

        FieldErrorStructure<List<CustomFieldError>> errorResponse = FieldErrorStructure
                .<List<CustomFieldError>>builder()
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .errorMessage("Validation failed for one or more fields. Please check the provided data.")
                .timestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .path(request.getRequestURI())
                .data(fieldErrors)
                .build();

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handles ConstraintViolationException for method-level validation failures.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with constraint violation details
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<FieldErrorStructure<List<CustomConstraintViolation>>> handleConstraintViolation(
            ConstraintViolationException ex, HttpServletRequest request) {
        
        log.warn("Constraint violation for request to: {}", request.getRequestURI());
        
        List<CustomConstraintViolation> violations = ex.getConstraintViolations()
                .stream()
                .map(violation -> CustomConstraintViolation.builder()
                        .propertyPath(violation.getPropertyPath().toString())
                        .invalidValue(violation.getInvalidValue())
                        .message(violation.getMessage())
                        .build())
                .collect(Collectors.toList());

        FieldErrorStructure<List<CustomConstraintViolation>> errorResponse = FieldErrorStructure
                .<List<CustomConstraintViolation>>builder()
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .errorMessage("Constraint validation failed for one or more parameters.")
                .timestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .path(request.getRequestURI())
                .data(violations)
                .build();

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Custom field error class for detailed validation error responses.
     */
    @Getter
    @Builder
    public static class CustomFieldError {
        private String field;
        private Object rejectedValue;
        private String errorMessage;
        private String objectName;
    }

    /**
     * Custom constraint violation class for method-level validation errors.
     */
    @Getter
    @Builder
    public static class CustomConstraintViolation {
        private String propertyPath;
        private Object invalidValue;
        private String message;
    }
}
