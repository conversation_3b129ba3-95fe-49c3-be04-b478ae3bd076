package com.example.movieticketbookingsystem.exception.handler;

import com.example.movieticketbookingsystem.exception.UserNotFoundByEmailException;
import com.example.movieticketbookingsystem.utility.ErrorStructure;
import com.example.movieticketbookingsystem.utility.RestResponseBuilder;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@AllArgsConstructor
public class LoginExceptionhandler{

    private final RestResponseBuilder restResponseBuilder;

    ResponseEntity<ErrorStructure> handleUsernameNotFoundException(UserNotFoundByEmailException exception){
        return restResponseBuilder.error(HttpStatus.UNAUTHORIZED,"No Username Found");
    }
}
