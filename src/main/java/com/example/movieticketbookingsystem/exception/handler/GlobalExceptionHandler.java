package com.example.movieticketbookingsystem.exception.handler;

import com.example.movieticketbookingsystem.exception.*;
import com.example.movieticketbookingsystem.utility.ErrorStructure;
import com.example.movieticketbookingsystem.utility.FieldErrorStructure;
import com.example.movieticketbookingsystem.utility.RestResponseBuilder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@RestControllerAdvice
@AllArgsConstructor
public class GlobalExceptionHandler {

    private final RestResponseBuilder restResponseBuilder;

    // User-related exceptions
    @ExceptionHandler(UserNotFoundByEmailException.class)
    public ResponseEntity<ErrorStructure> handleUserNotFoundByEmail(
            UserNotFoundByEmailException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.NOT_FOUND,
                ex.getMessage() != null ? ex.getMessage() : "User not found with the provided email",
                request.getRequestURI()
        );
    }

    @ExceptionHandler(UserExistByEmailException.class)
    public ResponseEntity<ErrorStructure> handleUserExistByEmail(
            UserExistByEmailException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.CONFLICT,
                ex.getMessage() != null ? ex.getMessage() : "User already exists with this email",
                request.getRequestURI()
        );
    }

    @ExceptionHandler(UserNotRegistered.class)
    public ResponseEntity<ErrorStructure> handleUserNotRegistered(
            UserNotRegistered ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.UNAUTHORIZED,
                ex.getMessage() != null ? ex.getMessage() : "User is not registered in the system",
                request.getRequestURI()
        );
    }

    // Movie-related exceptions
    @ExceptionHandler(MovieNotFoundByIdException.class)
    public ResponseEntity<ErrorStructure> handleMovieNotFoundById(
            MovieNotFoundByIdException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.NOT_FOUND,
                ex.getMessage() != null ? ex.getMessage() : "Movie not found with the provided ID",
                request.getRequestURI()
        );
    }

    // Theater-related exceptions
    @ExceptionHandler(TheaterOwnerIdException.class)
    public ResponseEntity<ErrorStructure> handleTheaterOwnerIdException(
            TheaterOwnerIdException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.NOT_FOUND,
                ex.getMessage() != null ? ex.getMessage() : "Theater owner not found with the provided ID",
                request.getRequestURI()
        );
    }

    // Screen-related exceptions
    @ExceptionHandler(ScreenIdNotFoundException.class)
    public ResponseEntity<ErrorStructure> handleScreenIdNotFound(
            ScreenIdNotFoundException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.NOT_FOUND,
                ex.getMessage() != null ? ex.getMessage() : "Screen not found with the provided ID",
                request.getRequestURI()
        );
    }

    // Generic resource not found
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ErrorStructure> handleResourceNotFound(
            ResourceNotFoundException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.NOT_FOUND,
                ex.getMessage() != null ? ex.getMessage() : "Requested resource not found",
                request.getRequestURI()
        );
    }

    // Conflict exceptions
    @ExceptionHandler(ConflictException.class)
    public ResponseEntity<ErrorStructure> handleConflictException(
            ConflictException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.CONFLICT,
                ex.getMessage() != null ? ex.getMessage() : "Resource conflict occurred",
                request.getRequestURI()
        );
    }

    // Security-related exceptions
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ErrorStructure> handleBadCredentials(
            BadCredentialsException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.UNAUTHORIZED,
                "Invalid username or password",
                request.getRequestURI()
        );
    }

    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ErrorStructure> handleAuthenticationException(
            AuthenticationException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.UNAUTHORIZED,
                "Authentication failed: " + ex.getMessage(),
                request.getRequestURI()
        );
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ErrorStructure> handleAccessDenied(
            AccessDeniedException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.FORBIDDEN,
                "Access denied: You don't have permission to access this resource",
                request.getRequestURI()
        );
    }

    // Validation exceptions
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<FieldErrorStructure<List<CustomFieldError>>> handleValidationErrors(
            MethodArgumentNotValidException ex, HttpServletRequest request) {
        
        List<CustomFieldError> fieldErrors = ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(fieldError -> CustomFieldError.builder()
                        .field(fieldError.getField())
                        .rejectedValue(fieldError.getRejectedValue())
                        .errorMessage(fieldError.getDefaultMessage())
                        .build())
                .collect(Collectors.toList());

        FieldErrorStructure<List<CustomFieldError>> errorResponse = FieldErrorStructure
                .<List<CustomFieldError>>builder()
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .errorMessage("Validation failed for one or more fields")
                .timestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .path(request.getRequestURI())
                .data(fieldErrors)
                .build();

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    // Generic exception handler
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorStructure> handleGenericException(
            Exception ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "An unexpected error occurred: " + ex.getMessage(),
                request.getRequestURI()
        );
    }

    // Runtime exception handler
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ErrorStructure> handleRuntimeException(
            RuntimeException ex, HttpServletRequest request) {
        return restResponseBuilder.error(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "Runtime error occurred: " + ex.getMessage(),
                request.getRequestURI()
        );
    }

    // Custom field error class for validation responses
    @Getter
    @Builder
    public static class CustomFieldError {
        private String field;
        private Object rejectedValue;
        private String errorMessage;
    }
}
