package com.example.movieticketbookingsystem.utility;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
@Builder
public class ErrorStructure{

    private int statusCode;        //404

    @JsonProperty(namespace = "error_message")
    private String message;         // failed to update the user (error message)
}
